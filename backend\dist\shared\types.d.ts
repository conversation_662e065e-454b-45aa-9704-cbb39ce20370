export interface UserProfile {
    id: string;
    email: string;
    name?: string;
    subscription_tier: "Free" | "Basic" | "Pro";
    credits_remaining: number;
    is_active: boolean;
    last_login?: string;
    subscription_expires_at?: string;
    stripe_customer_id?: string;
    created_at: string;
    updated_at: string;
}
export interface AuthResult {
    success: boolean;
    user?: UserProfile;
    token?: string;
    error?: string;
}
export type DocumentFileType = "pdf" | "docx" | "txt" | "pptx";
export interface DocumentMetadata {
    id: string;
    user_id: string;
    filename: string;
    file_type: DocumentFileType;
    file_size: number;
    supabase_storage_path: string;
    uploaded_at: string;
    processing_status: "pending" | "processing" | "completed" | "failed";
    content_preview?: string;
}
export interface DocumentWithContent extends DocumentMetadata {
    content_text: string;
}
export type StudySetType = "flashcards" | "quiz";
export interface StudySet {
    id: string;
    user_id: string;
    name: string;
    type: StudySetType;
    is_ai_generated: boolean;
    source_documents?: {
        id: string;
        filename: string;
    }[];
    custom_prompt?: string;
    created_at: string;
    updated_at: string;
    flashcard_count?: number;
    quiz_question_count?: number;
    last_studied_at?: string;
}
export interface Flashcard {
    id: string;
    study_set_id: string;
    front: string;
    back: string;
    is_flagged: boolean;
    is_ai_generated: boolean;
    difficulty_level?: number;
    review_count: number;
    last_reviewed_at?: string;
}
export type QuestionType = "multiple_choice" | "select_all" | "true_false" | "short_answer";
export interface QuizQuestion {
    id: string;
    study_set_id: string;
    question_text: string;
    question_type: QuestionType;
    options?: string[];
    correct_answers: string[];
    explanation?: string;
    is_ai_generated: boolean;
    difficulty_level?: number;
    times_answered: number;
    times_correct: number;
}
export interface CreditTransaction {
    id: string;
    user_id: string;
    credits_used: number;
    operation_type: string;
    description: string;
    metadata?: Record<string, any>;
    study_set_id?: string;
    created_at: string;
}
export interface AIOperationCost {
    operation_type: string;
    credits_required: number;
    is_active: boolean;
}
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T> extends APIResponse<T[]> {
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
}
export interface BaseComponentProps {
    className?: string;
    "data-testid"?: string;
}
export interface ButtonProps extends BaseComponentProps {
    children: React.ReactNode;
    onClick?: () => void;
    variant?: "primary" | "secondary" | "danger";
    size?: "sm" | "md" | "lg";
    isLoading?: boolean;
    disabled?: boolean;
    type?: "button" | "submit" | "reset";
}
export interface InputProps extends BaseComponentProps {
    label?: string;
    placeholder?: string;
    value: string;
    onChange: (value: string) => void;
    type?: "text" | "email" | "password" | "number";
    error?: string;
    required?: boolean;
    disabled?: boolean;
}
export interface ModalProps extends BaseComponentProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
    children: React.ReactNode;
    size?: "sm" | "md" | "lg" | "xl";
}
export interface StudySession {
    id: string;
    user_id: string;
    study_set_id: string;
    session_type: StudySetType;
    started_at: string;
    ended_at?: string;
    total_items: number;
    completed_items: number;
    correct_answers?: number;
    session_data?: Record<string, any>;
}
export interface StudyProgress {
    study_set_id: string;
    total_flashcards?: number;
    reviewed_flashcards?: number;
    flagged_flashcards?: number;
    total_quiz_questions?: number;
    answered_questions?: number;
    correct_answers?: number;
    last_studied_at?: string;
    study_streak?: number;
}
export interface AIGenerationRequest {
    documents: string[];
    studySetType: StudySetType;
    customPrompt?: string;
    itemCount?: number;
}
export interface AIGenerationResult {
    studySetId: string;
    itemsGenerated: number;
    creditsUsed: number;
    processingTime: number;
}
export type LoadingState = "idle" | "loading" | "success" | "error";
export interface ErrorState {
    message: string;
    code?: string;
    details?: Record<string, any>;
}
export type SortDirection = "asc" | "desc";
export interface SortConfig {
    field: string;
    direction: SortDirection;
}
export interface FilterConfig {
    field: string;
    value: any;
    operator?: "eq" | "ne" | "gt" | "lt" | "gte" | "lte" | "in" | "like";
}
//# sourceMappingURL=types.d.ts.map