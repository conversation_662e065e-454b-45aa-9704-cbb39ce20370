"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authenticateToken = void 0;
const supabaseService_1 = require("../services/supabaseService");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(" ")[1];
        if (!token) {
            return res
                .status(401)
                .json({ success: false, error: "Access token required" });
        }
        const { data: { user }, error, } = await supabaseService_1.supabaseService.verifyToken(token);
        if (error || !user) {
            return res
                .status(401)
                .json({ success: false, error: "Invalid or expired token" });
        }
        const profile = await supabaseService_1.supabaseService.getUserProfile(user.id);
        if (!profile || !profile.is_active) {
            return res
                .status(401)
                .json({ success: false, error: "User account not found or inactive" });
        }
        req.user = { id: user.id, email: user.email, profile };
        next();
    }
    catch {
        return res
            .status(500)
            .json({ success: false, error: "Authentication service error" });
    }
};
exports.authenticateToken = authenticateToken;
const optionalAuth = async (req, _res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(" ")[1];
        if (token) {
            const { data: { user }, } = await supabaseService_1.supabaseService.verifyToken(token);
            if (user) {
                const profile = await supabaseService_1.supabaseService.getUserProfile(user.id);
                if (profile && profile.is_active) {
                    req.user = { id: user.id, email: user.email, profile };
                }
            }
        }
        next();
    }
    catch {
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map